-- SmartEstimate 2.0 Database Schema
-- DXF Data Extraction System

CREATE DATABASE IF NOT EXISTS smartestimate_db;
USE smartestimate_db;

-- Users table for authentication
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHA<PERSON>(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'user') DEFAULT 'user',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    is_active BOOLEAN DEFAULT TRUE
);

-- Projects table
CREATE TABLE projects (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_name VARCHAR(200) NOT NULL,
    description TEXT,
    client_name VARCHAR(100),
    project_code VARCHAR(50) UNIQUE,
    status ENUM('active', 'completed', 'on_hold', 'cancelled') DEFAULT 'active',
    created_by INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE
);

-- DXF Files table
CREATE TABLE dxf_files (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    file_name VARCHAR(255) NOT NULL,
    original_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(500) NOT NULL,
    file_size BIGINT NOT NULL,
    upload_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    processed BOOLEAN DEFAULT FALSE,
    processed_at TIMESTAMP NULL,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- DXF Data table - stores extracted data from DXF files
CREATE TABLE dxf_data (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    dxf_file_id INT NOT NULL,
    data_type VARCHAR(50) NOT NULL, -- e.g., 'line', 'circle', 'arc', 'text', etc.
    layer_name VARCHAR(100),
    coordinates JSON, -- Store coordinate data as JSON
    properties JSON, -- Store additional properties as JSON
    measurements JSON, -- Store measurements (length, area, etc.) as JSON
    extracted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE,
    FOREIGN KEY (dxf_file_id) REFERENCES dxf_files(id) ON DELETE CASCADE
);

-- Project summaries table - aggregated data per project
CREATE TABLE project_summaries (
    id INT AUTO_INCREMENT PRIMARY KEY,
    project_id INT NOT NULL,
    total_files INT DEFAULT 0,
    total_entities INT DEFAULT 0,
    total_length DECIMAL(15,4) DEFAULT 0,
    total_area DECIMAL(15,4) DEFAULT 0,
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (project_id) REFERENCES projects(id) ON DELETE CASCADE
);

-- Insert default admin user (password: admin123)
INSERT INTO users (username, email, password_hash, full_name, role) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'System Administrator', 'admin');

-- Create indexes for better performance
CREATE INDEX idx_projects_status ON projects(status);
CREATE INDEX idx_projects_created_by ON projects(created_by);
CREATE INDEX idx_dxf_files_project ON dxf_files(project_id);
CREATE INDEX idx_dxf_files_processed ON dxf_files(processed);
CREATE INDEX idx_dxf_data_project ON dxf_data(project_id);
CREATE INDEX idx_dxf_data_file ON dxf_data(dxf_file_id);
CREATE INDEX idx_dxf_data_type ON dxf_data(data_type);
