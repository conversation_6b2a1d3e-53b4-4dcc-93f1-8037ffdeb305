# SmartEstimate 2.0 - DXF Data Extraction System

A PHP + Python application for extracting data from DXF files and managing project information.

## Features
- User authentication system
- DXF file processing with Python
- Project management dashboard
- MySQL database integration
- Modern, responsive UI design

## Project Structure
```
/
├── config/
│   ├── database.php
│   └── config.php
├── includes/
│   ├── auth.php
│   ├── functions.php
│   └── header.php
├── assets/
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── main.js
│   └── images/
├── python/
│   ├── dxf_processor.py
│   └── requirements.txt
├── pages/
│   ├── login.php
│   ├── dashboard.php
│   ├── projects.php
│   └── manage_project.php
├── uploads/
└── index.php
```

## Setup Instructions
1. Configure database settings in `config/database.php`
2. Install Python dependencies: `pip install -r python/requirements.txt`
3. Import database schema
4. Configure web server to point to project root

## Requirements
- PHP 7.4+
- Python 3.8+
- MySQL 5.7+
- Web server (Apache/Nginx)
