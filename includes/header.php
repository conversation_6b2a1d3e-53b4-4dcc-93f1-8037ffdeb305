<?php
/**
 * Common Header Component
 * SmartEstimate 2.0 - DXF Data Extraction System
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/auth.php';

$auth = new Auth();
$currentUser = $auth->getCurrentUser();

// Redirect to login if not authenticated
if (!$auth->isLoggedIn()) {
    redirect('pages/login.php');
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . APP_NAME : APP_NAME; ?></title>
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/feather-icons"></script>
</head>
<body>
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo">
                    <a href="index.php"><?php echo APP_NAME; ?></a>
                </div>
                
                <nav class="nav-menu">
                    <a href="index.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'index.php' ? 'active' : ''; ?>">
                        Dashboard
                    </a>
                    <a href="pages/projects.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'projects.php' ? 'active' : ''; ?>">
                        Projects
                    </a>
                    <?php if ($auth->isAdmin()): ?>
                    <a href="pages/users.php" class="nav-link <?php echo basename($_SERVER['PHP_SELF']) == 'users.php' ? 'active' : ''; ?>">
                        Users
                    </a>
                    <?php endif; ?>
                </nav>
                
                <div class="user-menu">
                    <div class="user-avatar">
                        <?php echo strtoupper(substr($currentUser['full_name'], 0, 1)); ?>
                    </div>
                    <div class="user-info">
                        <span class="user-name"><?php echo htmlspecialchars($currentUser['full_name']); ?></span>
                        <span class="user-role"><?php echo ucfirst($currentUser['role']); ?></span>
                    </div>
                    <a href="pages/logout.php" class="btn btn-secondary btn-sm">
                        Logout
                    </a>
                </div>
            </div>
        </div>
    </header>
    
    <main class="main-content">
        <div class="container">
