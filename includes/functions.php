<?php
/**
 * Common Functions
 * SmartEstimate 2.0 - DXF Data Extraction System
 */

require_once __DIR__ . '/../config/config.php';

/**
 * Project Management Functions
 */
class ProjectManager {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Get all projects
     */
    public function getAllProjects($user_id = null) {
        try {
            $sql = "
                SELECT p.*, u.full_name as created_by_name,
                       COUNT(df.id) as file_count,
                       ps.total_entities, ps.total_length, ps.total_area
                FROM projects p
                LEFT JOIN users u ON p.created_by = u.id
                LEFT JOIN dxf_files df ON p.id = df.project_id
                LEFT JOIN project_summaries ps ON p.id = ps.project_id
            ";
            
            if ($user_id) {
                $sql .= " WHERE p.created_by = :user_id";
            }
            
            $sql .= " GROUP BY p.id ORDER BY p.updated_at DESC";
            
            $stmt = $this->db->prepare($sql);
            if ($user_id) {
                $stmt->bindParam(':user_id', $user_id);
            }
            $stmt->execute();
            
            return $stmt->fetchAll();
        } catch (PDOException $e) {
            error_log("Get projects error: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get project by ID
     */
    public function getProject($id) {
        try {
            $stmt = $this->db->prepare("
                SELECT p.*, u.full_name as created_by_name
                FROM projects p
                LEFT JOIN users u ON p.created_by = u.id
                WHERE p.id = :id
            ");
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            
            return $stmt->fetch();
        } catch (PDOException $e) {
            error_log("Get project error: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Create new project
     */
    public function createProject($data) {
        try {
            $stmt = $this->db->prepare("
                INSERT INTO projects (project_name, description, client_name, project_code, created_by)
                VALUES (:project_name, :description, :client_name, :project_code, :created_by)
            ");
            
            $stmt->bindParam(':project_name', $data['project_name']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':client_name', $data['client_name']);
            $stmt->bindParam(':project_code', $data['project_code']);
            $stmt->bindParam(':created_by', $data['created_by']);
            
            if ($stmt->execute()) {
                return $this->db->lastInsertId();
            }
            
            return false;
        } catch (PDOException $e) {
            error_log("Create project error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Update project
     */
    public function updateProject($id, $data) {
        try {
            $stmt = $this->db->prepare("
                UPDATE projects 
                SET project_name = :project_name, 
                    description = :description, 
                    client_name = :client_name,
                    status = :status
                WHERE id = :id
            ");
            
            $stmt->bindParam(':id', $id);
            $stmt->bindParam(':project_name', $data['project_name']);
            $stmt->bindParam(':description', $data['description']);
            $stmt->bindParam(':client_name', $data['client_name']);
            $stmt->bindParam(':status', $data['status']);
            
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Update project error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Delete project
     */
    public function deleteProject($id) {
        try {
            $stmt = $this->db->prepare("DELETE FROM projects WHERE id = :id");
            $stmt->bindParam(':id', $id);
            return $stmt->execute();
        } catch (PDOException $e) {
            error_log("Delete project error: " . $e->getMessage());
            return false;
        }
    }
}

/**
 * File Management Functions
 */
class FileManager {
    private $db;
    
    public function __construct() {
        $this->db = getDB();
    }
    
    /**
     * Upload DXF file
     */
    public function uploadDXFFile($project_id, $file) {
        try {
            // Validate file
            if (!$this->validateDXFFile($file)) {
                return ['success' => false, 'message' => 'Invalid file type or size'];
            }
            
            // Generate unique filename
            $filename = uniqid() . '_' . $file['name'];
            $filepath = UPLOAD_DIR . $filename;
            
            // Move uploaded file
            if (move_uploaded_file($file['tmp_name'], $filepath)) {
                // Save to database
                $stmt = $this->db->prepare("
                    INSERT INTO dxf_files (project_id, file_name, original_name, file_path, file_size)
                    VALUES (:project_id, :file_name, :original_name, :file_path, :file_size)
                ");
                
                $stmt->bindParam(':project_id', $project_id);
                $stmt->bindParam(':file_name', $filename);
                $stmt->bindParam(':original_name', $file['name']);
                $stmt->bindParam(':file_path', $filepath);
                $stmt->bindParam(':file_size', $file['size']);
                
                if ($stmt->execute()) {
                    return ['success' => true, 'file_id' => $this->db->lastInsertId()];
                }
            }
            
            return ['success' => false, 'message' => 'File upload failed'];
        } catch (Exception $e) {
            error_log("File upload error: " . $e->getMessage());
            return ['success' => false, 'message' => 'File upload failed'];
        }
    }
    
    /**
     * Validate DXF file
     */
    private function validateDXFFile($file) {
        // Check file size
        if ($file['size'] > MAX_FILE_SIZE) {
            return false;
        }
        
        // Check file extension
        $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        return in_array($extension, ALLOWED_EXTENSIONS);
    }
}
?>
