<?php
/**
 * Manage Project Page (Add/Edit)
 * SmartEstimate 2.0 - DXF Data Extraction System
 */

$pageTitle = 'Manage Project';
require_once __DIR__ . '/../includes/header.php';
require_once __DIR__ . '/../includes/functions.php';

$projectManager = new ProjectManager();
$error = '';
$success = '';
$project = null;
$isEdit = false;

// Check if editing existing project
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $project = $projectManager->getProject($_GET['id']);
    if ($project) {
        $isEdit = true;
        $pageTitle = 'Edit Project';
    } else {
        $error = 'Project not found';
    }
}

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $projectData = [
        'project_name' => sanitizeInput($_POST['project_name'] ?? ''),
        'description' => sanitizeInput($_POST['description'] ?? ''),
        'client_name' => sanitizeInput($_POST['client_name'] ?? ''),
        'project_code' => sanitizeInput($_POST['project_code'] ?? ''),
        'status' => sanitizeInput($_POST['status'] ?? 'active'),
        'created_by' => $currentUser['id']
    ];
    
    // Validation
    if (empty($projectData['project_name'])) {
        $error = 'Project name is required';
    } elseif (empty($projectData['project_code'])) {
        $error = 'Project code is required';
    } else {
        if ($isEdit) {
            // Update existing project
            if ($projectManager->updateProject($project['id'], $projectData)) {
                $success = 'Project updated successfully';
                $project = $projectManager->getProject($project['id']); // Refresh data
            } else {
                $error = 'Failed to update project';
            }
        } else {
            // Create new project
            $projectId = $projectManager->createProject($projectData);
            if ($projectId) {
                $success = 'Project created successfully';
                redirect('../index.php');
            } else {
                $error = 'Failed to create project';
            }
        }
    }
}
?>

<div class="page-header">
    <h1 class="page-title"><?php echo $isEdit ? 'Edit Project' : 'New Project'; ?></h1>
    <p class="page-subtitle"><?php echo $isEdit ? 'Update project information' : 'Create a new project for DXF file processing'; ?></p>
</div>

<?php if ($error): ?>
    <div class="alert alert-error">
        <?php echo htmlspecialchars($error); ?>
    </div>
<?php endif; ?>

<?php if ($success): ?>
    <div class="alert alert-success">
        <?php echo htmlspecialchars($success); ?>
    </div>
<?php endif; ?>

<div class="card">
    <div class="card-header">
        <h2 class="card-title">Project Information</h2>
    </div>
    
    <div class="card-body">
        <form method="POST" class="project-form">
            <div class="grid grid-cols-2">
                <div class="form-group">
                    <label for="project_name" class="form-label">Project Name *</label>
                    <input 
                        type="text" 
                        id="project_name" 
                        name="project_name" 
                        class="form-input"
                        value="<?php echo htmlspecialchars($project['project_name'] ?? ''); ?>"
                        required
                        placeholder="Enter project name"
                    >
                </div>
                
                <div class="form-group">
                    <label for="project_code" class="form-label">Project Code *</label>
                    <input 
                        type="text" 
                        id="project_code" 
                        name="project_code" 
                        class="form-input"
                        value="<?php echo htmlspecialchars($project['project_code'] ?? ''); ?>"
                        required
                        placeholder="e.g., PRJ-001"
                        <?php echo $isEdit ? 'readonly' : ''; ?>
                    >
                </div>
            </div>
            
            <div class="grid grid-cols-2">
                <div class="form-group">
                    <label for="client_name" class="form-label">Client Name</label>
                    <input 
                        type="text" 
                        id="client_name" 
                        name="client_name" 
                        class="form-input"
                        value="<?php echo htmlspecialchars($project['client_name'] ?? ''); ?>"
                        placeholder="Enter client name"
                    >
                </div>
                
                <?php if ($isEdit): ?>
                <div class="form-group">
                    <label for="status" class="form-label">Status</label>
                    <select id="status" name="status" class="form-select">
                        <option value="active" <?php echo ($project['status'] ?? '') === 'active' ? 'selected' : ''; ?>>Active</option>
                        <option value="completed" <?php echo ($project['status'] ?? '') === 'completed' ? 'selected' : ''; ?>>Completed</option>
                        <option value="on_hold" <?php echo ($project['status'] ?? '') === 'on_hold' ? 'selected' : ''; ?>>On Hold</option>
                        <option value="cancelled" <?php echo ($project['status'] ?? '') === 'cancelled' ? 'selected' : ''; ?>>Cancelled</option>
                    </select>
                </div>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="description" class="form-label">Description</label>
                <textarea 
                    id="description" 
                    name="description" 
                    class="form-textarea"
                    rows="4"
                    placeholder="Enter project description"
                ><?php echo htmlspecialchars($project['description'] ?? ''); ?></textarea>
            </div>
            
            <div class="form-actions" style="display: flex; gap: 1rem; justify-content: flex-end; margin-top: 2rem;">
                <a href="../index.php" class="btn btn-secondary">Cancel</a>
                <button type="submit" class="btn btn-primary">
                    <?php echo $isEdit ? 'Update Project' : 'Create Project'; ?>
                </button>
            </div>
        </form>
    </div>
</div>

<?php require_once __DIR__ . '/../includes/footer.php'; ?>
