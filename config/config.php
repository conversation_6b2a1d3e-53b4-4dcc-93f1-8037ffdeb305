<?php
/**
 * Application Configuration
 * SmartEstimate 2.0 - DXF Data Extraction System
 */

// Start session if not already started
if (session_status() == PHP_SESSION_NONE) {
    session_start();
}

// Application settings
define('APP_NAME', 'SmartEstimate 2.0');
define('APP_VERSION', '2.0.0');
define('BASE_URL', 'http://localhost/smartestimate');

// File upload settings
define('UPLOAD_DIR', __DIR__ . '/../uploads/');
define('MAX_FILE_SIZE', 50 * 1024 * 1024); // 50MB
define('ALLOWED_EXTENSIONS', ['dxf', 'dwg']);

// Python script settings
define('PYTHON_SCRIPT_PATH', __DIR__ . '/../python/dxf_processor.py');
define('PYTHON_EXECUTABLE', 'python3');

// Database settings
require_once __DIR__ . '/database.php';

// Timezone
date_default_timezone_set('UTC');

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Helper functions
function redirect($url) {
    // Ensure we have a clean URL
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        // If it's a relative URL, make it absolute
        if (strpos($url, '/') === 0) {
            $url = 'http://' . $_SERVER['HTTP_HOST'] . $url;
        } else {
            $url = 'http://' . $_SERVER['HTTP_HOST'] . '/' . $url;
        }
    }
    header("Location: $url");
    exit();
}

function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

function requireLogin() {
    if (!isLoggedIn()) {
        redirect('pages/login.php');
    }
}

function sanitizeInput($data) {
    return htmlspecialchars(strip_tags(trim($data)));
}

function formatDate($date) {
    return date('M d, Y H:i', strtotime($date));
}
?>
