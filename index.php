<?php
/**
 * Main Dashboard - Index Page
 * SmartEstimate 2.0 - DXF Data Extraction System
 */

$pageTitle = 'Dashboard';
require_once 'includes/header.php';
require_once 'includes/functions.php';

$projectManager = new ProjectManager();
$projects = $projectManager->getAllProjects();

// Calculate statistics
$totalProjects = count($projects);
$activeProjects = count(array_filter($projects, function($p) { return $p['status'] === 'active'; }));
$completedProjects = count(array_filter($projects, function($p) { return $p['status'] === 'completed'; }));
$totalFiles = array_sum(array_column($projects, 'file_count'));
?>

<div class="page-header">
    <h1 class="page-title">Dashboard</h1>
    <p class="page-subtitle">Welcome back, <?php echo htmlspecialchars($currentUser['full_name']); ?>! Here's an overview of your projects.</p>
</div>

<!-- Statistics Cards -->
<div class="stats-grid">
    <div class="stat-card">
        <div class="stat-value"><?php echo $totalProjects; ?></div>
        <div class="stat-label">Total Projects</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-value"><?php echo $activeProjects; ?></div>
        <div class="stat-label">Active Projects</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-value"><?php echo $completedProjects; ?></div>
        <div class="stat-label">Completed Projects</div>
    </div>
    
    <div class="stat-card">
        <div class="stat-value"><?php echo $totalFiles; ?></div>
        <div class="stat-label">DXF Files Processed</div>
    </div>
</div>

<!-- Projects Table -->
<div class="card">
    <div class="card-header">
        <div class="flex justify-between items-center">
            <h2 class="card-title">Recent Projects</h2>
            <a href="pages/manage_project.php" class="btn btn-primary">
                <i data-feather="plus"></i>
                New Project
            </a>
        </div>
    </div>
    
    <div class="table-container">
        <?php if (empty($projects)): ?>
            <div class="card-body text-center">
                <div style="padding: 3rem;">
                    <i data-feather="folder" style="width: 48px; height: 48px; color: var(--gray-400); margin-bottom: 1rem;"></i>
                    <h3 style="color: var(--gray-600); margin-bottom: 0.5rem;">No Projects Yet</h3>
                    <p style="color: var(--gray-500); margin-bottom: 1.5rem;">Get started by creating your first project.</p>
                    <a href="pages/manage_project.php" class="btn btn-primary">
                        <i data-feather="plus"></i>
                        Create Project
                    </a>
                </div>
            </div>
        <?php else: ?>
            <table class="table">
                <thead>
                    <tr>
                        <th>Project Name</th>
                        <th>Client</th>
                        <th>Status</th>
                        <th>Files</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($projects as $project): ?>
                    <tr>
                        <td>
                            <div>
                                <div class="font-weight-600"><?php echo htmlspecialchars($project['project_name']); ?></div>
                                <?php if ($project['project_code']): ?>
                                    <div class="text-muted" style="font-size: 0.75rem;"><?php echo htmlspecialchars($project['project_code']); ?></div>
                                <?php endif; ?>
                            </div>
                        </td>
                        <td><?php echo htmlspecialchars($project['client_name'] ?: 'N/A'); ?></td>
                        <td>
                            <?php
                            $statusClass = '';
                            switch ($project['status']) {
                                case 'active':
                                    $statusClass = 'badge-success';
                                    break;
                                case 'completed':
                                    $statusClass = 'badge-info';
                                    break;
                                case 'on_hold':
                                    $statusClass = 'badge-warning';
                                    break;
                                case 'cancelled':
                                    $statusClass = 'badge-error';
                                    break;
                                default:
                                    $statusClass = 'badge-secondary';
                            }
                            ?>
                            <span class="badge <?php echo $statusClass; ?>">
                                <?php echo ucfirst(str_replace('_', ' ', $project['status'])); ?>
                            </span>
                        </td>
                        <td>
                            <span class="text-primary font-weight-600"><?php echo $project['file_count']; ?></span>
                        </td>
                        <td>
                            <div><?php echo formatDate($project['created_at']); ?></div>
                            <div class="text-muted" style="font-size: 0.75rem;">by <?php echo htmlspecialchars($project['created_by_name']); ?></div>
                        </td>
                        <td>
                            <div class="action-buttons">
                                <a href="pages/project_details.php?id=<?php echo $project['id']; ?>" 
                                   class="btn btn-secondary btn-sm btn-icon" 
                                   title="View Details">
                                    <i data-feather="eye"></i>
                                </a>
                                <a href="pages/manage_project.php?id=<?php echo $project['id']; ?>" 
                                   class="btn btn-secondary btn-sm btn-icon" 
                                   title="Edit">
                                    <i data-feather="edit"></i>
                                </a>
                                <a href="pages/upload_dxf.php?project_id=<?php echo $project['id']; ?>" 
                                   class="btn btn-primary btn-sm btn-icon" 
                                   title="Upload DXF">
                                    <i data-feather="upload"></i>
                                </a>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        <?php endif; ?>
    </div>
</div>

<?php require_once 'includes/footer.php'; ?>
