#!/usr/bin/env python3
"""
DXF File Processor
SmartEstimate 2.0 - DXF Data Extraction System

This script processes DXF files and extracts relevant data for storage in MySQL database.
Usage: python dxf_processor.py --file <dxf_file_path> --project_id <project_id> --file_id <file_id>
"""

import sys
import argparse
import json
import os
from datetime import datetime
import mysql.connector
from mysql.connector import Error
import ezdxf
from ezdxf import recover
import logging

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('dxf_processor.log'),
        logging.StreamHandler(sys.stdout)
    ]
)

class DXFProcessor:
    def __init__(self, db_config):
        """Initialize DXF processor with database configuration"""
        self.db_config = db_config
        self.connection = None
        
    def connect_database(self):
        """Establish database connection"""
        try:
            self.connection = mysql.connector.connect(**self.db_config)
            if self.connection.is_connected():
                logging.info("Successfully connected to MySQL database")
                return True
        except Error as e:
            logging.error(f"Error connecting to MySQL: {e}")
            return False
        
    def disconnect_database(self):
        """Close database connection"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logging.info("MySQL connection closed")
    
    def process_dxf_file(self, file_path, project_id, file_id):
        """
        Process DXF file and extract data
        
        Args:
            file_path (str): Path to DXF file
            project_id (int): Project ID
            file_id (int): File ID in database
            
        Returns:
            dict: Processing results
        """
        try:
            logging.info(f"Processing DXF file: {file_path}")
            
            # Try to read DXF file
            try:
                doc = ezdxf.readfile(file_path)
            except IOError:
                logging.warning(f"File {file_path} not found or not readable, trying recovery...")
                doc, auditor = recover.readfile(file_path)
                if auditor.has_errors:
                    logging.warning(f"DXF file has errors: {auditor.errors}")
            
            # Extract data from DXF
            extracted_data = self.extract_dxf_data(doc)
            
            # Store data in database
            if self.connect_database():
                self.store_extracted_data(project_id, file_id, extracted_data)
                self.update_file_status(file_id)
                self.update_project_summary(project_id)
                self.disconnect_database()
            
            logging.info(f"Successfully processed DXF file: {file_path}")
            return {
                'success': True,
                'message': 'DXF file processed successfully',
                'data': extracted_data
            }
            
        except Exception as e:
            logging.error(f"Error processing DXF file: {e}")
            return {
                'success': False,
                'message': f'Error processing DXF file: {str(e)}'
            }
    
    def extract_dxf_data(self, doc):
        """
        Extract data from DXF document
        
        Args:
            doc: DXF document object
            
        Returns:
            dict: Extracted data
        """
        extracted_data = {
            'entities': [],
            'layers': [],
            'summary': {
                'total_entities': 0,
                'total_length': 0.0,
                'total_area': 0.0,
                'entity_counts': {}
            }
        }
        
        # Get all layers
        for layer in doc.layers:
            extracted_data['layers'].append({
                'name': layer.dxf.name,
                'color': layer.dxf.color,
                'linetype': layer.dxf.linetype
            })
        
        # Process modelspace entities
        msp = doc.modelspace()
        
        for entity in msp:
            entity_data = self.extract_entity_data(entity)
            if entity_data:
                extracted_data['entities'].append(entity_data)
                
                # Update summary
                extracted_data['summary']['total_entities'] += 1
                entity_type = entity_data['type']
                extracted_data['summary']['entity_counts'][entity_type] = \
                    extracted_data['summary']['entity_counts'].get(entity_type, 0) + 1
                
                # Add to total length/area if applicable
                if 'length' in entity_data['measurements']:
                    extracted_data['summary']['total_length'] += entity_data['measurements']['length']
                if 'area' in entity_data['measurements']:
                    extracted_data['summary']['total_area'] += entity_data['measurements']['area']
        
        return extracted_data
    
    def extract_entity_data(self, entity):
        """
        Extract data from individual entity
        
        Args:
            entity: DXF entity object
            
        Returns:
            dict: Entity data
        """
        try:
            entity_data = {
                'type': entity.dxftype(),
                'layer': entity.dxf.layer,
                'coordinates': [],
                'properties': {},
                'measurements': {}
            }
            
            # Extract coordinates and measurements based on entity type
            if entity.dxftype() == 'LINE':
                start = entity.dxf.start
                end = entity.dxf.end
                entity_data['coordinates'] = [
                    {'x': start.x, 'y': start.y, 'z': start.z},
                    {'x': end.x, 'y': end.y, 'z': end.z}
                ]
                entity_data['measurements']['length'] = start.distance(end)
                
            elif entity.dxftype() == 'CIRCLE':
                center = entity.dxf.center
                radius = entity.dxf.radius
                entity_data['coordinates'] = [{'x': center.x, 'y': center.y, 'z': center.z}]
                entity_data['properties']['radius'] = radius
                entity_data['measurements']['circumference'] = 2 * 3.14159 * radius
                entity_data['measurements']['area'] = 3.14159 * radius * radius
                
            elif entity.dxftype() == 'ARC':
                center = entity.dxf.center
                radius = entity.dxf.radius
                start_angle = entity.dxf.start_angle
                end_angle = entity.dxf.end_angle
                entity_data['coordinates'] = [{'x': center.x, 'y': center.y, 'z': center.z}]
                entity_data['properties'].update({
                    'radius': radius,
                    'start_angle': start_angle,
                    'end_angle': end_angle
                })
                # Calculate arc length
                angle_diff = end_angle - start_angle
                if angle_diff < 0:
                    angle_diff += 360
                entity_data['measurements']['length'] = (angle_diff / 360) * 2 * 3.14159 * radius
                
            elif entity.dxftype() == 'LWPOLYLINE':
                points = []
                for point in entity.get_points():
                    points.append({'x': point[0], 'y': point[1], 'z': 0})
                entity_data['coordinates'] = points
                entity_data['properties']['closed'] = entity.closed
                # Calculate total length
                total_length = 0
                for i in range(len(points) - 1):
                    p1, p2 = points[i], points[i + 1]
                    length = ((p2['x'] - p1['x'])**2 + (p2['y'] - p1['y'])**2)**0.5
                    total_length += length
                entity_data['measurements']['length'] = total_length
                
            elif entity.dxftype() == 'TEXT':
                insert = entity.dxf.insert
                entity_data['coordinates'] = [{'x': insert.x, 'y': insert.y, 'z': insert.z}]
                entity_data['properties'].update({
                    'text': entity.dxf.text,
                    'height': entity.dxf.height,
                    'rotation': entity.dxf.rotation
                })
            
            return entity_data
            
        except Exception as e:
            logging.warning(f"Error extracting data from entity {entity.dxftype()}: {e}")
            return None
    
    def store_extracted_data(self, project_id, file_id, extracted_data):
        """Store extracted data in database"""
        try:
            cursor = self.connection.cursor()
            
            # Insert each entity
            for entity in extracted_data['entities']:
                insert_query = """
                    INSERT INTO dxf_data (project_id, dxf_file_id, data_type, layer_name, 
                                        coordinates, properties, measurements)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """
                cursor.execute(insert_query, (
                    project_id,
                    file_id,
                    entity['type'],
                    entity['layer'],
                    json.dumps(entity['coordinates']),
                    json.dumps(entity['properties']),
                    json.dumps(entity['measurements'])
                ))
            
            self.connection.commit()
            logging.info(f"Stored {len(extracted_data['entities'])} entities in database")
            
        except Error as e:
            logging.error(f"Error storing data in database: {e}")
            self.connection.rollback()
    
    def update_file_status(self, file_id):
        """Update file processing status"""
        try:
            cursor = self.connection.cursor()
            update_query = """
                UPDATE dxf_files 
                SET processed = TRUE, processed_at = %s 
                WHERE id = %s
            """
            cursor.execute(update_query, (datetime.now(), file_id))
            self.connection.commit()
            
        except Error as e:
            logging.error(f"Error updating file status: {e}")
    
    def update_project_summary(self, project_id):
        """Update project summary statistics"""
        try:
            cursor = self.connection.cursor()
            
            # Calculate summary statistics
            summary_query = """
                SELECT 
                    COUNT(DISTINCT df.id) as total_files,
                    COUNT(dd.id) as total_entities,
                    COALESCE(SUM(JSON_EXTRACT(dd.measurements, '$.length')), 0) as total_length,
                    COALESCE(SUM(JSON_EXTRACT(dd.measurements, '$.area')), 0) as total_area
                FROM projects p
                LEFT JOIN dxf_files df ON p.id = df.project_id
                LEFT JOIN dxf_data dd ON p.id = dd.project_id
                WHERE p.id = %s
            """
            cursor.execute(summary_query, (project_id,))
            result = cursor.fetchone()
            
            if result:
                # Update or insert project summary
                upsert_query = """
                    INSERT INTO project_summaries (project_id, total_files, total_entities, total_length, total_area)
                    VALUES (%s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    total_files = VALUES(total_files),
                    total_entities = VALUES(total_entities),
                    total_length = VALUES(total_length),
                    total_area = VALUES(total_area)
                """
                cursor.execute(upsert_query, (project_id, *result))
                self.connection.commit()
                
        except Error as e:
            logging.error(f"Error updating project summary: {e}")

def main():
    """Main function to run DXF processor"""
    parser = argparse.ArgumentParser(description='Process DXF files and extract data')
    parser.add_argument('--file', required=True, help='Path to DXF file')
    parser.add_argument('--project_id', required=True, type=int, help='Project ID')
    parser.add_argument('--file_id', required=True, type=int, help='File ID')
    
    args = parser.parse_args()
    
    # Database configuration (should match PHP config)
    db_config = {
        'host': 'localhost',
        'database': 'smartestimate_db',
        'user': 'root',
        'password': ''
    }
    
    # Process DXF file
    processor = DXFProcessor(db_config)
    result = processor.process_dxf_file(args.file, args.project_id, args.file_id)
    
    # Output result as JSON for PHP to read
    print(json.dumps(result))
    
    return 0 if result['success'] else 1

if __name__ == '__main__':
    sys.exit(main())
